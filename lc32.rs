struct Solution;

/*
最长有效括号
可以使用动态规划 dp[i] 表示以 i 结尾的最长有效括号长度
dp[i] = dp[i-2] + 2 if s[i-1] == '(' && s[i] == ')' else 0
 */

impl Solution {
    pub fn longest_valid_parentheses(s: String) -> i32 {
        let mut stack = vec![-1];
        let mut max_len = 0;

        for (i, c) in s.chars().enumerate() {
            if c == '(' {
                stack.push(i as i32);
            } else {
                stack.pop();
                if stack.is_empty() {
                    stack.push(i as i32);
                } else {
                    max_len = max_len.max(i as i32 - stack.last().unwrap());
                }
            }
        }
        max_len
    }
}