struct Solution;

impl Solution {
    /// 方法1: 动态规划
    /// 时间复杂度: O(n), 空间复杂度: O(n)
    /// 思路: 预计算每个位置左边和右边的最大高度，然后计算每个位置能接的雨水
    pub fn trap_dp(height: Vec<i32>) -> i32 {
        let n = height.len();
        if n <= 2 {
            return 0;
        }
        
        // left_max[i] 表示位置 i 左边（包括i）的最大高度
        let mut left_max = vec![0; n];
        // right_max[i] 表示位置 i 右边（包括i）的最大高度
        let mut right_max = vec![0; n];
        
        // 计算左边最大值
        left_max[0] = height[0];
        for i in 1..n {
            left_max[i] = left_max[i - 1].max(height[i]);
        }
        
        // 计算右边最大值
        right_max[n - 1] = height[n - 1];
        for i in (0..n - 1).rev() {
            right_max[i] = right_max[i + 1].max(height[i]);
        }
        
        // 计算每个位置能接的雨水
        let mut water = 0;
        for i in 0..n {
            // 当前位置能接的雨水 = min(左边最高, 右边最高) - 当前高度
            let min_height = left_max[i].min(right_max[i]);
            if min_height > height[i] {
                water += min_height - height[i];
            }
        }
        
        water
    }
    
    /// 方法2: 双指针
    /// 时间复杂度: O(n), 空间复杂度: O(1)
    /// 思路: 使用两个指针从两端向中间移动，维护左右最大值
    pub fn trap_two_pointers(height: Vec<i32>) -> i32 {
        let n = height.len();
        if n <= 2 {
            return 0;
        }
        
        let mut left = 0;
        let mut right = n - 1;
        let mut left_max = 0;
        let mut right_max = 0;
        let mut water = 0;
        
        while left < right {
            if height[left] < height[right] {
                // 左边较低，处理左边
                if height[left] >= left_max {
                    left_max = height[left];
                } else {
                    water += left_max - height[left];
                }
                left += 1;
            } else {
                // 右边较低，处理右边
                if height[right] >= right_max {
                    right_max = height[right];
                } else {
                    water += right_max - height[right];
                }
                right -= 1;
            }
        }
        
        water
    }
    
    /// 方法3: 单调栈
    /// 时间复杂度: O(n), 空间复杂度: O(n)
    /// 思路: 维护一个单调递减栈，当遇到比栈顶高的柱子时，计算能接的雨水
    pub fn trap_stack(height: Vec<i32>) -> i32 {
        let n = height.len();
        if n <= 2 {
            return 0;
        }
        
        let mut stack: Vec<usize> = Vec::new();
        let mut water = 0;
        
        for i in 0..n {
            // 当当前高度大于栈顶高度时，可以形成凹槽接雨水
            while !stack.is_empty() && height[i] > height[*stack.last().unwrap()] {
                let top = stack.pop().unwrap();
                
                if stack.is_empty() {
                    break;
                }
                
                let left = *stack.last().unwrap();
                let width = i - left - 1;
                let min_height = height[left].min(height[i]);
                let h = min_height - height[top];
                
                water += (width * h as usize) as i32;
            }
            
            stack.push(i);
        }
        
        water
    }
}

fn main() {
    let test_cases = vec![
        vec![0,1,0,2,1,0,1,3,2,1,2,1], // 期望输出: 6
        vec![4,2,0,3,2,5],             // 期望输出: 9
        vec![3,0,2,0,4],               // 期望输出: 7
        vec![0,1,0,2,1,0,1,3,2,1,2,1], // 期望输出: 6
        vec![],                        // 期望输出: 0
        vec![1],                       // 期望输出: 0
        vec![1,2],                     // 期望输出: 0
    ];
    
    for (i, height) in test_cases.iter().enumerate() {
        println!("测试用例 {}: {:?}", i + 1, height);
        
        let result_dp = Solution::trap_dp(height.clone());
        let result_two_pointers = Solution::trap_two_pointers(height.clone());
        let result_stack = Solution::trap_stack(height.clone());
        
        println!("  动态规划解法: {}", result_dp);
        println!("  双指针解法: {}", result_two_pointers);
        println!("  单调栈解法: {}", result_stack);
        println!();
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    
    #[test]
    fn test_trap_dp() {
        assert_eq!(Solution::trap_dp(vec![0,1,0,2,1,0,1,3,2,1,2,1]), 6);
        assert_eq!(Solution::trap_dp(vec![4,2,0,3,2,5]), 9);
        assert_eq!(Solution::trap_dp(vec![3,0,2,0,4]), 7);
        assert_eq!(Solution::trap_dp(vec![]), 0);
        assert_eq!(Solution::trap_dp(vec![1]), 0);
        assert_eq!(Solution::trap_dp(vec![1,2]), 0);
    }
    
    #[test]
    fn test_trap_two_pointers() {
        assert_eq!(Solution::trap_two_pointers(vec![0,1,0,2,1,0,1,3,2,1,2,1]), 6);
        assert_eq!(Solution::trap_two_pointers(vec![4,2,0,3,2,5]), 9);
        assert_eq!(Solution::trap_two_pointers(vec![3,0,2,0,4]), 7);
        assert_eq!(Solution::trap_two_pointers(vec![]), 0);
        assert_eq!(Solution::trap_two_pointers(vec![1]), 0);
        assert_eq!(Solution::trap_two_pointers(vec![1,2]), 0);
    }
    
    #[test]
    fn test_trap_stack() {
        assert_eq!(Solution::trap_stack(vec![0,1,0,2,1,0,1,3,2,1,2,1]), 6);
        assert_eq!(Solution::trap_stack(vec![4,2,0,3,2,5]), 9);
        assert_eq!(Solution::trap_stack(vec![3,0,2,0,4]), 7);
        assert_eq!(Solution::trap_stack(vec![]), 0);
        assert_eq!(Solution::trap_stack(vec![1]), 0);
        assert_eq!(Solution::trap_stack(vec![1,2]), 0);
    }
    
    #[test]
    fn test_all_methods_consistency() {
        let test_cases = vec![
            vec![0,1,0,2,1,0,1,3,2,1,2,1],
            vec![4,2,0,3,2,5],
            vec![3,0,2,0,4],
            vec![],
            vec![1],
            vec![1,2],
            vec![2,1,2],
            vec![3,2,0,4],
        ];
        
        for height in test_cases {
            let result_dp = Solution::trap_dp(height.clone());
            let result_two_pointers = Solution::trap_two_pointers(height.clone());
            let result_stack = Solution::trap_stack(height.clone());
            
            assert_eq!(result_dp, result_two_pointers, "DP和双指针结果不一致: {:?}", height);
            assert_eq!(result_dp, result_stack, "DP和单调栈结果不一致: {:?}", height);
        }
    }
}
