struct Solution;

impl Solution {
    pub fn generate_parenthesis(n: i32) -> Vec<String> {
        let mut result = Vec::new();
        let mut current = String::new();
        Self::backtrack(&mut result, &mut current, 0, 0, n);
        result
    }
    
    fn backtrack(result: &mut Vec<String>, current: &mut String, open: i32, close: i32, max: i32) {
        // 如果当前字符串长度等于2*n，说明找到一个有效组合
        if current.len() == (2 * max) as usize {
            result.push(current.clone());
            return;
        }
        
        // 如果左括号数量小于n，可以添加左括号
        if open < max {
            current.push('(');
            Self::backtrack(result, current, open + 1, close, max);
            current.pop();
        }
        
        // 如果右括号数量小于左括号数量，可以添加右括号
        if close < open {
            current.push(')');
            Self::backtrack(result, current, open, close + 1, max);
            current.pop();
        }
    }
}

fn main() {
    let test_cases = vec![1, 2, 3, 4];
    
    println!("生成括号组合测试结果：");
    println!("===================");
    
    for n in test_cases {
        let result = Solution::generate_parenthesis(n);
        println!("n = {} 的结果:", n);
        for (i, combination) in result.iter().enumerate() {
            println!("  {}: \"{}\"", i + 1, combination);
        }
        println!("总共 {} 种组合\n", result.len());
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    
    #[test]
    fn test_n_1() {
        let result = Solution::generate_parenthesis(1);
        let expected = vec!["()"];
        assert_eq!(result, expected);
    }
    
    #[test]
    fn test_n_2() {
        let result = Solution::generate_parenthesis(2);
        let mut expected = vec!["(())", "()()"];
        expected.sort();
        let mut actual = result;
        actual.sort();
        assert_eq!(actual, expected);
    }
    
    #[test]
    fn test_n_3() {
        let result = Solution::generate_parenthesis(3);
        let mut expected = vec!["((()))", "(()())", "(())()", "()(())", "()()()"];
        expected.sort();
        let mut actual = result;
        actual.sort();
        assert_eq!(actual, expected);
    }
    
    #[test]
    fn test_result_count() {
        // 卡塔兰数：C(n) = (2n)! / ((n+1)! * n!)
        // n=1: 1, n=2: 2, n=3: 5, n=4: 14
        assert_eq!(Solution::generate_parenthesis(1).len(), 1);
        assert_eq!(Solution::generate_parenthesis(2).len(), 2);
        assert_eq!(Solution::generate_parenthesis(3).len(), 5);
        assert_eq!(Solution::generate_parenthesis(4).len(), 14);
    }
    
    #[test]
    fn test_all_valid() {
        // 测试所有生成的括号组合都是有效的
        for n in 1..=4 {
            let result = Solution::generate_parenthesis(n);
            for combination in result {
                assert!(is_valid_parentheses(&combination), 
                       "Invalid combination: {}", combination);
            }
        }
    }
}

// 辅助函数：检查括号组合是否有效
fn is_valid_parentheses(s: &str) -> bool {
    let mut count = 0;
    for ch in s.chars() {
        match ch {
            '(' => count += 1,
            ')' => {
                count -= 1;
                if count < 0 {
                    return false;
                }
            }
            _ => return false,
        }
    }
    count == 0
}
